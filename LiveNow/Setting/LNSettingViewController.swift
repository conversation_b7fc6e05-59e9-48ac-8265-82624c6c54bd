//
//  LNSettingViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit

/// 使用 UITableView 的分组样式实现 Settings 页面
/// - iOS 13+
/// - 动态字体与深色模式
/// - 使用 SnapKit 进行布局，尺寸使用 `s(xx)` 适配
final class LNSettingViewController: LNBaseController {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部白色背景
    override var useTopWhiteBackground: Bool { return true }

    // MARK: - Constants
    private enum Keys { static let dnd = "LNDoNotDisturbEnabled" }

    private enum Section: Int, CaseIterable {
        case dnd
        case quick
        case docs
        case version
    }

    private enum Row: Hashable {
        case doNotDisturb
        case blacklist
        case help
        case deleteAccount
        case userAgreement
        case privacy
        case terms
        case childSafety
        case about
        case appVersion
        case clearCache
    }

    private let rows: [[Row]] = [
        [.doNotDisturb],
        [.blacklist, .help, .deleteAccount],
        [.userAgreement, .privacy, .terms, .childSafety, .about],
        [.appVersion, .clearCache]
    ]

    private enum ReuseId {
        static let `default` = "LNSettingCellDefault"
        static let value1 = "LNSettingCellValue1"
    }

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .insetGrouped)
        tv.dataSource = self
        tv.delegate = self
        tv.estimatedRowHeight = s(56)
        tv.rowHeight = s(56)
        tv.backgroundColor = .clear
        tv.separatorStyle = .none
        tv.register(UITableViewCell.self, forCellReuseIdentifier: ReuseId.default)
        tv.register(UITableViewCell.self, forCellReuseIdentifier: ReuseId.value1)
        return tv
    }()

    // 底部登出按钮
    private lazy var logoutButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Logout", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.layer.cornerRadius = s(26)
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(logoutTapped), for: .touchUpInside)
        return b
    }()
    private let logoutGradient = CAGradientLayer()

    // 标志位，避免重复设置渐变层frame导致动画
    private var isLogoutGradientConfigured = false

    // 缓存显示文本
    private var cacheSizeText: String = "--"

    // DoNotDisturb状态
    private var doNotDisturbStatus: Bool = false

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Settings"
        edgesForExtendedLayout = .all
        setupTableView()
        applyLogoutFooter()
        refreshCacheSize()
        loadDoNotDisturbStatus()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 只在渐变层已配置且bounds发生变化时才更新frame
        if isLogoutGradientConfigured && !logoutGradient.frame.equalTo(logoutButton.bounds) {
            // 禁用隐式动画，避免渐变层frame变化时产生动画效果
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            logoutGradient.frame = logoutButton.bounds
            CATransaction.commit()
        }
    }

    // MARK: - Setup
    private func setupTableView() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(knavH)
            make.left.right.bottom.equalToSuperview()
        }
    }

    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    private func applyLogoutFooter() {
        // 容器，保证分组页脚外观更接近设计图
        let container = UIView(frame: CGRect(x: 0, y: 0, width: kscreenW, height: s(120)))
        container.backgroundColor = .clear
        container.addSubview(logoutButton)
        logoutButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.bottom.equalToSuperview().offset(-s(24))
            make.height.equalTo(s(52))
        }

        // 立即设置渐变层配置
        logoutGradient.colors = LNGradient.primaryColors
        logoutGradient.startPoint = LNGradient.primaryStartPoint
        logoutGradient.endPoint = LNGradient.primaryEndPoint

        // 设置初始frame，避免等待布局
        let buttonWidth = kscreenW - s(48) // 左右各24的间距
        let buttonHeight = s(52)
        logoutGradient.frame = CGRect(x: 0, y: 0, width: buttonWidth, height: buttonHeight)

        // 禁用隐式动画并插入渐变层
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        logoutButton.layer.insertSublayer(logoutGradient, at: 0)
        CATransaction.commit()

        tableView.tableFooterView = container
        isLogoutGradientConfigured = true
    }

    // MARK: - DoNotDisturb API Methods
    private func loadDoNotDisturbStatus() {
        NetWorkRequest(LNApiProfile.disturbStatus, completion: { [weak self] result in
            DispatchQueue.main.async {
                self?.handleDoNotDisturbStatusResponse(result)
            }
        }, failure: { [weak self] error in
            DispatchQueue.main.async {
                self?.handleDoNotDisturbError(error)
            }
        })
    }

    private func handleDoNotDisturbStatusResponse(_ result: [String: Any]) {
        // 根据实际API返回格式解析数据
        if let dataDict = result["data"] as? [String: Any],
           let isNotDisturb = dataDict["isNotDisturb"] as? Int {
            // isNotDisturb: 0表示未开启免打扰，1表示开启免打扰
            let isEnabled = (isNotDisturb == 1)
            doNotDisturbStatus = isEnabled

            // 更新UI中的开关状态
            if let cell = tableView.cellForRow(at: IndexPath(row: 0, section: 0)),
               let switchControl = cell.accessoryView as? UISwitch {
                switchControl.setOn(isEnabled, animated: false)
            }
            // 同时更新UserDefaults以保持一致性
            UserDefaults.standard.set(isEnabled, forKey: Keys.dnd)

            // 如果有过期时间，可以记录日志
            if let expireSecs = dataDict["disturbExpireSecs"] as? Int {
                Log("DoNotDisturb状态: \(isEnabled ? "开启" : "关闭"), 过期时间: \(expireSecs)秒")
            }
        }
    }

    private func handleDoNotDisturbError(_ error: NSError) {
        Log("获取DoNotDisturb状态失败: \(error.localizedDescription)")
        // 使用本地缓存的状态作为备用
        doNotDisturbStatus = UserDefaults.standard.bool(forKey: Keys.dnd)
    }

    private func setDoNotDisturbStatus(_ isOpen: Bool, sender: UISwitch) {
        // 禁用开关，防止重复点击
        sender.isEnabled = false

        let params = ["isOpen": isOpen ? "1" : "0"]

        NetWorkRequest(LNApiProfile.disturbSwitch(par: params), completion: { [weak self] result in
            DispatchQueue.main.async {
                sender.isEnabled = true
                self?.handleDoNotDisturbSwitchResponse(result, isOpen: isOpen, sender: sender)
            }
        }, failure: { [weak self] error in
            DispatchQueue.main.async {
                sender.isEnabled = true
                self?.handleDoNotDisturbSwitchError(error, sender: sender)
            }
        })
    }

    private func handleDoNotDisturbSwitchResponse(_ result: [String: Any], isOpen: Bool, sender: UISwitch) {
        // 检查返回的success字段和code字段
        if let success = result["success"] as? Int, success == 1,
           let code = result["code"] as? Int, code == 200 {

            // 根据实际返回数据格式解析状态
            var finalStatus = isOpen // 默认使用请求的状态

            if let dataDict = result["data"] as? [String: Any],
               let isNotDisturb = dataDict["isNotDisturb"] as? Int {
                finalStatus = (isNotDisturb == 1)
            }

            // 服务器返回成功，更新本地状态
            doNotDisturbStatus = finalStatus
            UserDefaults.standard.set(finalStatus, forKey: Keys.dnd)

            // 确保UI状态与服务器返回的状态一致
            if sender.isOn != finalStatus {
                sender.setOn(finalStatus, animated: true)
            }

            Log("DoNotDisturb状态设置成功: \(finalStatus)")
        } else {
            // 服务器返回失败，恢复开关状态
            sender.setOn(!isOpen, animated: true)
            UserDefaults.standard.set(!isOpen, forKey: Keys.dnd)

            let msg = result["msg"] as? String ?? "设置失败"
            let code = result["code"] as? Int ?? -1
            Log("DoNotDisturb状态设置失败: code=\(code), msg=\(msg)")


        }
    }

    private func handleDoNotDisturbSwitchError(_ error: NSError, sender: UISwitch) {
        // 网络错误，恢复开关状态
        sender.setOn(!sender.isOn, animated: true)
        UserDefaults.standard.set(!sender.isOn, forKey: Keys.dnd)

        Log("DoNotDisturb状态设置网络错误: \(error.localizedDescription)")

        // 显示网络错误提示
        showErrorMessage("网络连接失败，请检查网络后重试")
    }

    // MARK: - Actions
    @objc private func dndChanged(_ sender: UISwitch) {
        let wasOff = !doNotDisturbStatus

        // 调用API设置状态
        setDoNotDisturbStatus(sender.isOn, sender: sender)

        // 只有当从关闭变为开启时才显示提醒弹窗
        if sender.isOn && wasOff {
            showDoNotDisturbReminder()
        }
    }

    private func showDoNotDisturbReminder() {
        let modal = LNDoNotDisturbModal()
        modal.onConfirm = { [weak modal] in
            modal?.dismiss()
        }
        modal.onCancel = { [weak self, weak modal] in
            // 用户取消，将开关设置回关闭状态
            if let cell = self?.tableView.cellForRow(at: IndexPath(row: 0, section: 0)),
               let switchControl = cell.accessoryView as? UISwitch {
                switchControl.setOn(false, animated: true)
                UserDefaults.standard.set(false, forKey: Keys.dnd)
            }
            modal?.dismiss()
        }

        // 显示在window级别，覆盖整个屏幕包括导航栏
        if let window = view.window {
            modal.show(in: window)
        } else {
            // 备用方案：显示在导航控制器的view上
            modal.show(in: navigationController?.view ?? view)
        }
    }

    @objc private func logoutTapped() {
        let alert = UIAlertController(title: "Logout", message: "You have been logged out (demo).", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func openBlacklist() { navigationController?.pushViewController(LNBlacklistViewController(), animated: true) }
    private func openHelp() { navigationController?.pushViewController(LNHelpFeedbackViewController(), animated: true) }
    private func openDeleteAccount() { navigationController?.pushViewController(LNDeleteAccountPreController(), animated: true) }

    private func openDoc(title: String) {
        let alert = UIAlertController(title: title, message: "Open web page here.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func confirmClearCache() {
        let alert = UIAlertController(title: "Clear cache", message: "Remove cached files?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Clear", style: .destructive, handler: { _ in
            self.clearCaches()
        }))
        present(alert, animated: true)
    }

    // MARK: - Cache Helpers
    private func refreshCacheSize() {
        DispatchQueue.global(qos: .utility).async {
            let size = Self.cacheDirectorySize()
            let text = Self.formatBytes(size)
            DispatchQueue.main.async {
                self.cacheSizeText = text
                if let index = self.indexPath(of: .clearCache) {
                    self.tableView.reloadRows(at: [index], with: .none)
                }
            }
        }
    }

    private func clearCaches() {
        let fm = FileManager.default
        if let url = fm.urls(for: .cachesDirectory, in: .userDomainMask).first {
            if let items = try? fm.contentsOfDirectory(atPath: url.path) {
                for item in items {
                    let p = url.appendingPathComponent(item).path
                    try? fm.removeItem(atPath: p)
                }
            }
        }
        refreshCacheSize()
    }

    private static func cacheDirectorySize() -> Int64 {
        let fm = FileManager.default
        guard let url = fm.urls(for: .cachesDirectory, in: .userDomainMask).first else { return 0 }
        var total: Int64 = 0
        if let enumerator = fm.enumerator(at: url, includingPropertiesForKeys: [.fileSizeKey], options: [], errorHandler: nil) {
            for case let fileURL as URL in enumerator {
                if let size = try? fileURL.resourceValues(forKeys: [.fileSizeKey]).fileSize { total += Int64(size) }
            }
        }
        return total
    }

    private static func formatBytes(_ bytes: Int64) -> String {
        let kb: Double = 1024
        let mb = kb * 1024
        let gb = mb * 1024
        let b = Double(bytes)
        if b >= gb { return String(format: "%.2fGB", b/gb) }
        if b >= mb { return String(format: "%.2fMB", b/mb) }
        if b >= kb { return String(format: "%.2fKB", b/kb) }
        return String(format: "%.0fB", b)
    }

    private static var appVersion: String {
        let v = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0"
        let b = Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "1"
        return "v\(v) (\(b))"
    }
}

// MARK: - UITableViewDataSource & Delegate
extension LNSettingViewController: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int { Section.allCases.count }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { rows[section].count }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return section == 0 ? s(10) : s(8)
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat { s(0.01) }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let row = rows[indexPath.section][indexPath.row]

        // 统一出队
        func dequeue(_ style: UITableViewCell.CellStyle) -> UITableViewCell {
            switch style {
            case .value1: return tableView.dequeueReusableCell(withIdentifier: ReuseId.value1) ?? UITableViewCell(style: .value1, reuseIdentifier: ReuseId.value1)
            default: return tableView.dequeueReusableCell(withIdentifier: ReuseId.default) ?? UITableViewCell(style: .default, reuseIdentifier: ReuseId.default)
            }
        }

        // 统一配置标题与 accessory 样式
        func configureDefault(title: String, accessory: UITableViewCell.AccessoryType = .none) -> UITableViewCell {
            let cell = dequeue(.default)
            cell.textLabel?.text = title
            cell.accessoryType = accessory
            cell.selectionStyle = .default
            cell.accessoryView = nil
            return cell
        }

        switch row {
        case .doNotDisturb:
            let cell = dequeue(.default)
            cell.textLabel?.text = "Do Not Disturb"
            cell.selectionStyle = .none
            let sw = UISwitch()
            sw.isOn = doNotDisturbStatus
            sw.onTintColor = UIColor(hexString: "#52E35B")
            sw.addTarget(self, action: #selector(dndChanged(_:)), for: .valueChanged)
            cell.accessoryView = sw
            cell.accessoryType = .none
            return cell

        case .blacklist:
            return configureDefault(title: "Blacklist", accessory: .disclosureIndicator)

        case .help:
            return configureDefault(title: "Help and Feed back", accessory: .disclosureIndicator)

        case .deleteAccount:
            return configureDefault(title: "Delete account", accessory: .disclosureIndicator)

        case .userAgreement:
            return configureDefault(title: "User Agreement", accessory: .disclosureIndicator)

        case .privacy:
            return configureDefault(title: "Privacy Policy", accessory: .disclosureIndicator)

        case .terms:
            return configureDefault(title: "Terms of Use", accessory: .disclosureIndicator)

        case .childSafety:
            return configureDefault(title: "Child Safety Standards policy", accessory: .disclosureIndicator)

        case .about:
            return configureDefault(title: "About Us", accessory: .disclosureIndicator)

        case .appVersion:
            let cell = dequeue(.value1)
            cell.textLabel?.text = "Version"
            cell.detailTextLabel?.text = Self.appVersion
            cell.selectionStyle = .none
            cell.accessoryType = .none
            cell.accessoryView = nil
            return cell

        case .clearCache:
            let cell = dequeue(.value1)
            cell.textLabel?.text = "Clear cache"
            cell.detailTextLabel?.text = cacheSizeText
            cell.accessoryType = .disclosureIndicator
            cell.accessoryView = nil
            return cell
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let row = rows[indexPath.section][indexPath.row]
        switch row {
        case .blacklist: openBlacklist()
        case .help: openHelp()
        case .deleteAccount: openDeleteAccount()
        case .userAgreement: openDoc(title: "User Agreement")
        case .privacy: openDoc(title: "Privacy Policy")
        case .terms: openDoc(title: "Terms of Use")
        case .childSafety: openDoc(title: "Child Safety Standards policy")
        case .about: openDoc(title: "About Us")
        case .clearCache: confirmClearCache()
        case .doNotDisturb, .appVersion: break
        }
    }

    // MARK: - Helpers
    private func indexPath(of target: Row) -> IndexPath? {
        for (sectionIdx, r) in rows.enumerated() {
            if let rowIdx = r.firstIndex(of: target) { return IndexPath(row: rowIdx, section: sectionIdx) }
        }
        return nil
    }
}

